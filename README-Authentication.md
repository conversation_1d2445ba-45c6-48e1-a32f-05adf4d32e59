# 🔐 Système d'Authentification AquaTrack Mobile

Ce système d'authentification utilise la table `utilisateur` de la base de données "Facturation" pour une application mobile React Native.

## 📁 Structure des fichiers

### Backend (Node.js)
- **`server/auth.js`** - Serveur d'authentification complet
  - Port: 4000
  - Base de données: PostgreSQL "Facturation"
  - Table: `utilisateur`

### Frontend (React Native)
- **`react-native/AuthMobile.js`** - Interface mobile d'authentification
  - Écran de connexion
  - Dashboard utilisateur
  - Gestion des sessions

### Base de données
- **`database/utilisateur-test-data.sql`** - Données de test pour les utilisateurs

## 🗄️ Structure de la table utilisateur

```sql
CREATE TABLE utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100),
    password VARCHAR(100),
    role VARCHAR(10),
    is_protected BOOLEAN
);
```

## 🚀 Installation et Configuration

### 1. Prérequis
- Node.js installé
- PostgreSQL installé
- Base de données "Facturation" créée
- React Native environment configuré

### 2. Installation des dépendances backend
```bash
cd server
npm install express cors pg
```

### 3. Configuration de la base de données
```bash
# Connectez-vous à PostgreSQL
psql -U postgres -d Facturation

# Exécutez le script de données de test
\i database/utilisateur-test-data.sql
```

### 4. Démarrage du serveur backend
```bash
cd server
node auth.js
```

Le serveur démarre sur `http://localhost:4000`

### 5. Configuration React Native
```bash
cd react-native
npm install @react-native-async-storage/async-storage
```

## 📱 Utilisation de l'application mobile

### Comptes de test disponibles

#### Technicien
- **Email:** `<EMAIL>`
- **Mot de passe:** `Tech123`
- **Nom:** Ahmed Benali

#### Administrateur
- **Email:** `<EMAIL>`
- **Mot de passe:** `Admin123`
- **Nom:** Fatima Gharbi

### Fonctionnalités

#### Écran de connexion
- Saisie email/mot de passe
- Validation des champs
- Gestion des erreurs
- Indicateur de chargement

#### Dashboard utilisateur
- Affichage des informations du profil
- Actions selon le rôle (Tech/Admin)
- Déconnexion sécurisée

## 🔧 API Endpoints

### Authentification
```
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Tech123"
}
```

**Réponse de succès:**
```json
{
  "success": true,
  "message": "Connexion réussie",
  "user": {
    "id": 1,
    "nom": "Benali",
    "prenom": "Ahmed",
    "email": "<EMAIL>",
    "role": "Tech",
    "adresse": "123 Rue de la Technologie, Tunis",
    "tel": "71234567",
    "is_protected": false
  },
  "token": "mobile_token_1_1704067200000"
}
```

### Profil utilisateur
```
GET /api/user/:id
```

### Mise à jour profil
```
PUT /api/user/:id
Content-Type: application/json

{
  "nom": "Nouveau nom",
  "prenom": "Nouveau prénom",
  "adresse": "Nouvelle adresse",
  "tel": "Nouveau téléphone",
  "email": "<EMAIL>"
}
```

## 🔒 Sécurité

- Validation des champs côté serveur
- Gestion des erreurs appropriée
- Sessions utilisateur avec tokens
- Stockage sécurisé des données utilisateur (AsyncStorage)

## 🧪 Tests

### Test du backend
```bash
# Tester la connexion
curl -X POST http://localhost:4000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Tech123"}'
```

### Test de l'API
```bash
# Vérifier le statut du serveur
curl http://localhost:4000/
```

## 📝 Logs du serveur

Le serveur affiche des logs détaillés :
- ✅ Connexions réussies
- ❌ Erreurs d'authentification
- 📱 Requêtes mobiles
- 🔍 Recherches d'utilisateurs

## 🔧 Personnalisation

### Modifier l'URL de l'API
Dans `react-native/AuthMobile.js`, changez :
```javascript
const API_BASE_URL = 'http://votre-serveur:4000';
```

### Ajouter de nouveaux utilisateurs
Exécutez dans PostgreSQL :
```sql
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) 
VALUES ('Nom', 'Prénom', 'Adresse', 'Téléphone', '<EMAIL>', 'motdepasse', 'Tech', false);
```

## 🐛 Dépannage

### Erreur de connexion à la base
- Vérifiez que PostgreSQL est démarré
- Vérifiez les paramètres de connexion dans `server/auth.js`
- Vérifiez que la base "Facturation" existe

### Erreur de connexion mobile
- Vérifiez que le serveur backend est démarré
- Vérifiez l'URL de l'API dans le code React Native
- Vérifiez la connectivité réseau

### Utilisateur non trouvé
- Vérifiez que les données de test sont insérées
- Vérifiez l'orthographe de l'email et du mot de passe

## 📞 Support

Pour toute question ou problème, vérifiez :
1. Les logs du serveur backend
2. Les logs de l'application mobile
3. La connectivité à la base de données
4. Les données de test dans la table utilisateur
