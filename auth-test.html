<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AquaTrack - Authentification</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .auth-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #2196F3;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .logo p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            background-color: #fff !important;
        }

        .form-group input:focus {
            outline: none;
            border-color: #2196F3;
            background-color: #fff !important;
        }

        .form-group input:hover {
            border-color: #2196F3;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.3s;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: #1976D2;
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .test-accounts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .test-accounts h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .test-accounts p {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .quick-fill-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            margin-right: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 5px;
        }

        .quick-fill-btn:hover {
            background: #45a049;
        }

        .quick-fill-btn.admin {
            background: #FF9800;
        }

        .quick-fill-btn.admin:hover {
            background: #f57c00;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .user-info {
            display: none;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .user-info h3 {
            color: #1976D2;
            margin-bottom: 15px;
        }

        .user-info p {
            margin-bottom: 8px;
            color: #333;
        }

        .logout-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
        }

        .logout-btn:hover {
            background: #d32f2f;
        }

        .loading {
            display: none;
            text-align: center;
            color: #666;
        }

        .clear-btn {
            background: #9E9E9E;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 5px;
        }

        .clear-btn:hover {
            background: #757575;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="logo">
            <h1>AquaTrack</h1>
            <p>Système de Facturation</p>
        </div>

        <div id="message" class="message"></div>
        <div id="loading" class="loading">Connexion en cours...</div>

        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required autocomplete="email">
            </div>

            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>

            <button type="submit" class="login-btn" id="loginBtn">Se connecter</button>
        </form>

        <div class="test-accounts">
            <h3>Comptes de test :</h3>
            <p><strong>Technicien:</strong> <EMAIL> / Tech123</p>
            <p><strong>Admin:</strong> <EMAIL> / Admin123</p>
            
            <div style="margin-top: 10px;">
                <button type="button" class="quick-fill-btn" onclick="fillTechAccount()">Remplir Tech</button>
                <button type="button" class="quick-fill-btn admin" onclick="fillAdminAccount()">Remplir Admin</button>
                <button type="button" class="clear-btn" onclick="clearFields()">Effacer</button>
            </div>
        </div>

        <div id="userInfo" class="user-info">
            <h3>Informations utilisateur</h3>
            <div id="userDetails"></div>
            <button type="button" class="logout-btn" onclick="logout()">Se déconnecter</button>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:4000';

        // Variables globales
        let emailInput, passwordInput, loginForm, loginBtn, loading, message, userInfo, userDetails;

        // Initialisation des éléments DOM
        function initializeElements() {
            emailInput = document.getElementById('email');
            passwordInput = document.getElementById('password');
            loginForm = document.getElementById('loginForm');
            loginBtn = document.getElementById('loginBtn');
            loading = document.getElementById('loading');
            message = document.getElementById('message');
            userInfo = document.getElementById('userInfo');
            userDetails = document.getElementById('userDetails');
        }

        // Fonction pour effacer les champs
        function clearFields() {
            if (emailInput && passwordInput) {
                emailInput.value = '';
                passwordInput.value = '';
                emailInput.focus();
            }
        }

        // Fonction pour remplir le compte technicien
        function fillTechAccount() {
            if (emailInput && passwordInput) {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'Tech123';
                passwordInput.focus();
            }
        }

        // Fonction pour remplir le compte admin
        function fillAdminAccount() {
            if (emailInput && passwordInput) {
                emailInput.value = '<EMAIL>';
                passwordInput.value = 'Admin123';
                passwordInput.focus();
            }
        }

        // Fonction pour afficher les messages
        function showMessage(text, type) {
            if (message) {
                message.textContent = text;
                message.className = `message ${type}`;
                message.style.display = 'block';

                // Masquer le message après 5 secondes
                setTimeout(() => {
                    message.style.display = 'none';
                }, 5000);
            }
        }

        // Fonction pour afficher les informations utilisateur
        function showUserInfo(user) {
            if (userInfo && userDetails) {
                userDetails.innerHTML = `
                    <p><strong>Nom:</strong> ${user.prenom} ${user.nom}</p>
                    <p><strong>Email:</strong> ${user.email}</p>
                    <p><strong>Rôle:</strong> ${user.role}</p>
                    <p><strong>Téléphone:</strong> ${user.tel || 'Non renseigné'}</p>
                    <p><strong>Adresse:</strong> ${user.adresse || 'Non renseignée'}</p>
                    <p><strong>ID:</strong> ${user.id}</p>
                `;

                userInfo.style.display = 'block';
            }
        }

        // Fonction de connexion
        async function handleLogin(e) {
            e.preventDefault();

            if (!emailInput || !passwordInput) {
                showMessage('Erreur: Éléments de formulaire non trouvés', 'error');
                return;
            }

            const email = emailInput.value.trim();
            const password = passwordInput.value;

            if (!email || !password) {
                showMessage('Veuillez saisir votre email et mot de passe', 'error');
                return;
            }

            // Afficher le loading
            if (loginBtn) loginBtn.disabled = true;
            if (loading) loading.style.display = 'block';
            if (message) message.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('Connexion réussie !', 'success');
                    showUserInfo(data.user);
                    if (loginForm) loginForm.style.display = 'none';
                } else {
                    showMessage(data.message || 'Erreur de connexion', 'error');
                }
            } catch (error) {
                console.error('Erreur:', error);
                showMessage('Erreur de connexion au serveur. Vérifiez que le serveur est démarré.', 'error');
            } finally {
                if (loginBtn) loginBtn.disabled = false;
                if (loading) loading.style.display = 'none';
            }
        }

        // Fonction de déconnexion
        function logout() {
            if (loginForm) loginForm.style.display = 'block';
            if (userInfo) userInfo.style.display = 'none';
            if (message) message.style.display = 'none';

            clearFields();
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page chargée, initialisation...');

            // Initialiser les éléments
            initializeElements();

            // Vérifier que les éléments sont bien trouvés
            if (!emailInput || !passwordInput || !loginForm) {
                console.error('Erreur: Éléments de formulaire non trouvés');
                return;
            }

            // Ajouter l'événement de soumission du formulaire
            loginForm.addEventListener('submit', handleLogin);

            // Permettre la saisie libre dans les champs
            emailInput.addEventListener('input', function() {
                // Rien de spécial, juste permettre la saisie
            });

            passwordInput.addEventListener('input', function() {
                // Rien de spécial, juste permettre la saisie
            });

            // Focus sur le premier champ
            emailInput.focus();

            console.log('Initialisation terminée');
        });

        // Fonction de test de connexion au serveur
        async function testServerConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/`);
                const data = await response.json();
                console.log('Serveur accessible:', data);
                return true;
            } catch (error) {
                console.error('Serveur non accessible:', error);
                return false;
            }
        }

        // Tester la connexion au serveur au chargement
        window.addEventListener('load', function() {
            setTimeout(testServerConnection, 1000);
        });
    </script>
</body>
</html>
