-- Script de données de test pour la table utilisateur
-- Base de données: Facturation
-- Exécuter ce script dans PostgreSQL pour créer des utilisateurs de test

-- Supprimer les données existantes (optionnel)
-- DELETE FROM utilisateur;

-- Insérer des utilisateurs de test
INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected) VALUES 
-- Utilisateur Technicien
('<PERSON><PERSON>', '<PERSON>', '123 Rue de la Technologie, Tunis', '71234567', '<EMAIL>', 'Tech123', 'Tech', false),

-- Utilisa<PERSON><PERSON>
('<PERSON><PERSON><PERSON>', 'Fatima', '456 Avenue de l''Administration, Tunis', '71345678', '<EMAIL>', 'Admin123', 'Admin', true),

-- Autres techniciens
('<PERSON><PERSON><PERSON>', '<PERSON>', '789 Rue des Compteurs, Ariana', '71456789', '<EMAIL>', 'Tech456', 'Tech', false),
('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '321 Boulevard des Mesures, Manouba', '71567890', '<EMAIL>', 'Tech789', 'Tech', false),
('Khelifi', 'Sami', '654 Rue de la Maintenance, Ben Arous', '71678901', '<EMAIL>', '<PERSON>101', 'Tech', false),

-- <PERSON>tres admins
('Bouazizi', '<PERSON>', '987 Place de la Gestion, Tunis', '71789012', '<EMAIL>', 'Admin456', 'Admin', false),
('Jemli', 'Karim', '147 Rue du Contrôle, Sfax', '74890123', '<EMAIL>', 'Admin789', 'Admin', false);

-- Vérification des données insérées
SELECT 
    'Utilisateurs créés' as info,
    COUNT(*) as total,
    SUM(CASE WHEN role = 'Tech' THEN 1 ELSE 0 END) as techniciens,
    SUM(CASE WHEN role = 'Admin' THEN 1 ELSE 0 END) as admins
FROM utilisateur;

-- Afficher tous les utilisateurs créés
SELECT 
    idtech,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    role,
    tel,
    CASE WHEN is_protected THEN 'Oui' ELSE 'Non' END as protege
FROM utilisateur
ORDER BY role, nom, prenom;

-- Afficher les informations de connexion pour les tests
SELECT 
    '=== COMPTES DE TEST POUR L''APPLICATION MOBILE ===' as info
UNION ALL
SELECT '' as info
UNION ALL
SELECT 'TECHNICIEN:' as info
UNION ALL
SELECT 'Email: <EMAIL>' as info
UNION ALL
SELECT 'Mot de passe: Tech123' as info
UNION ALL
SELECT 'Nom: Ahmed Benali' as info
UNION ALL
SELECT '' as info
UNION ALL
SELECT 'ADMINISTRATEUR:' as info
UNION ALL
SELECT 'Email: <EMAIL>' as info
UNION ALL
SELECT 'Mot de passe: Admin123' as info
UNION ALL
SELECT 'Nom: Fatima Gharbi' as info
UNION ALL
SELECT '' as info
UNION ALL
SELECT 'AUTRES TECHNICIENS:' as info
UNION ALL
SELECT '<EMAIL> / Tech456' as info
UNION ALL
SELECT '<EMAIL> / Tech789' as info
UNION ALL
SELECT '<EMAIL> / Tech101' as info
UNION ALL
SELECT '' as info
UNION ALL
SELECT 'AUTRES ADMINS:' as info
UNION ALL
SELECT '<EMAIL> / Admin456' as info
UNION ALL
SELECT '<EMAIL> / Admin789' as info;
