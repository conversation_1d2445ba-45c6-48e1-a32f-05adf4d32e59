import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';

const API_BASE_URL = 'http://localhost:4000';

const AuthenticationPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });
      
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        const user = data.user;
        
        Alert.alert(
          'Connexion réussie', 
          `Bienvenue ${user.prenom} ${user.nom}!\nRôle: ${user.role}`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Redirection selon le rôle
                if (user.role === 'Tech') {
                  console.log('👨‍🔧 Redirection vers technician-dashboard');
                  navigation.navigate('TechnicianDashboard', { user });
                } else if (user.role === 'Admin') {
                  console.log('👨‍💼 Redirection vers dashboard admin');
                  navigation.navigate('AdminDashboard', { user });
                } else {
                  Alert.alert('Erreur', 'Rôle utilisateur non reconnu');
                }
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message);
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion', 
        'Impossible de se connecter au serveur. Vérifiez que le serveur est démarré sur le port 4000.'
      );
    } finally {
      setLoading(false);
    }
  };

  const fillTestAccount = (type) => {
    if (type === 'tech') {
      setEmail('<EMAIL>');
      setPassword('Tech123');
    } else if (type === 'admin') {
      setEmail('<EMAIL>');
      setPassword('Admin123');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.authContainer}>
            {/* Logo/Titre */}
            <View style={styles.logo}>
              <Text style={styles.logoTitle}>AquaTrack</Text>
              <Text style={styles.logoSubtitle}>Système de Facturation</Text>
            </View>

            {/* Message de chargement */}
            {loading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#2196F3" />
                <Text style={styles.loadingText}>Connexion en cours...</Text>
              </View>
            )}

            {/* Formulaire de connexion */}
            <View style={styles.formContainer}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Mot de passe</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Entrez votre mot de passe"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>

              <TouchableOpacity 
                style={[styles.loginBtn, loading && styles.loginBtnDisabled]}
                onPress={handleLogin}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.loginBtnText}>Se connecter</Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Comptes de test */}
            <View style={styles.testAccounts}>
              <Text style={styles.testAccountsTitle}>Comptes de test :</Text>
              <Text style={styles.testAccountsText}>
                <Text style={styles.bold}>Technicien:</Text> <EMAIL> / Tech123
              </Text>
              <Text style={styles.testAccountsText}>
                <Text style={styles.bold}>Admin:</Text> <EMAIL> / Admin123
              </Text>
              
              <View style={styles.testButtonsContainer}>
                <TouchableOpacity 
                  style={[styles.testButton, styles.techButton]}
                  onPress={() => fillTestAccount('tech')}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>Remplir Tech</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.testButton, styles.adminButton]}
                  onPress={() => fillTestAccount('admin')}
                  disabled={loading}
                >
                  <Text style={styles.testButtonText}>Remplir Admin</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#667eea',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authContainer: {
    backgroundColor: 'white',
    padding: 40,
    borderRadius: 15,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 15,
    },
    shadowOpacity: 0.1,
    shadowRadius: 35,
    elevation: 10,
  },
  logo: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoTitle: {
    color: '#2196F3',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  logoSubtitle: {
    color: '#666',
    fontSize: 16,
  },
  loadingContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 14,
  },
  formContainer: {
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    marginBottom: 8,
    color: '#333',
    fontWeight: '600',
    fontSize: 16,
  },
  input: {
    width: '100%',
    padding: 12,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 8,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  loginBtn: {
    width: '100%',
    padding: 15,
    backgroundColor: '#2196F3',
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  loginBtnDisabled: {
    backgroundColor: '#ccc',
  },
  loginBtnText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  testAccounts: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  testAccountsTitle: {
    color: '#333',
    marginBottom: 10,
    fontSize: 14,
    fontWeight: '600',
  },
  testAccountsText: {
    color: '#666',
    fontSize: 12,
    marginBottom: 5,
  },
  bold: {
    fontWeight: 'bold',
  },
  testButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  testButton: {
    flex: 1,
    padding: 8,
    borderRadius: 3,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  techButton: {
    backgroundColor: '#4CAF50',
  },
  adminButton: {
    backgroundColor: '#FF9800',
  },
  testButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default AuthenticationPage;
