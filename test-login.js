const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('🔐 Test de connexion...');
    
    const response = await fetch('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Tech123'
      }),
    });

    const data = await response.json();
    console.log('📥 Réponse:', data);

    if (data.success) {
      console.log('✅ Connexion réussie !');
      console.log('👤 Utilisateur:', data.user.prenom, data.user.nom);
      console.log('🎯 Rôle:', data.user.role);
    } else {
      console.log('❌ Échec de la connexion:', data.message);
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  }
}

testLogin();
