const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',  
  password: '123456',
  port: 5432,
});

async function checkUsers() {
  try {
    console.log('🔍 Vérification des utilisateurs...');
    
    const query = 'SELECT idtech, nom, prenom, email, password, role FROM utilisateur ORDER BY role, nom';
    const result = await pool.query(query);
    
    console.log('👥 Utilisateurs trouvés:', result.rows.length);
    
    result.rows.forEach(user => {
      console.log(`- ID: ${user.idtech}`);
      console.log(`  Nom: ${user.prenom} ${user.nom}`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Password: ${user.password}`);
      console.log(`  Rôle: ${user.role}`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

checkUsers();
