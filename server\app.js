require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation', 
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Route de connexion (compatible avec votre frontend)
app.post('/login', async (req, res) => {
  console.log('Requête de connexion reçue:', req.body);
  const { email, motDepass } = req.body; // Utilise motDepass comme dans votre frontend

  try {
    // Validation des champs requis
    if (!email || !motDepass) {
      return res.status(400).json({
        success: false,
        message: "Email et mot de passe requis"
      });
    }

    // 1. Vérifier si l'utilisateur existe
    const userResult = await pool.query(
      'SELECT * FROM Utilisateur WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      console.log('Aucun utilisateur trouvé avec cet email:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = userResult.rows[0];
    console.log('Utilisateur trouvé:', { email: user.email, role: user.role });

    // 2. Vérifier le mot de passe (comparaison directe car stocké en clair)
    if (motDepass !== user.motdepass) {
      console.log('Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    // 3. Connexion réussie
    console.log('Connexion réussie pour:', email);
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });

  } catch (error) {
    console.error('Erreur de connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route de test
app.get('/', (req, res) => {
  res.send('Serveur Facturation fonctionnel');
});

// Démarrer le serveur
const PORT = process.env.PORT || 3001; // Port 3001 pour correspondre au frontend
app.listen(PORT, () => {
  console.log(`Serveur Facturation démarré sur http://localhost:${PORT}`);
});