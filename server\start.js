// Script de démarrage simple pour l'architecture modulaire
console.log('🚀 Démarrage du serveur TechnicianDashboard modulaire...\n');

// Vérification des modules requis
const requiredModules = [
  './database.js',
  './clients.js',
  './consommation.js',
  './factures.js',
  './codeQR.js'
];

console.log('📦 Vérification des modules...');
requiredModules.forEach(module => {
  try {
    require(module);
    console.log(`✅ ${module}`);
  } catch (error) {
    console.log(`❌ ${module} - Erreur: ${error.message}`);
  }
});

console.log('\n🔧 Démarrage du serveur principal...');

// Démarrer le serveur principal
require('./TechnicianDashboard.js');
