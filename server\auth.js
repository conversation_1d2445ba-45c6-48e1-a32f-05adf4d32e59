const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',  
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données "Facturation" réussie');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack',
    status: 'Fonctionnel',
    database: 'Facturation',
    port: PORT
  });
});

// Route de connexion pour mobile
app.post('/api/auth/login', async (req, res) => {
  console.log('� Requête de connexion mobile reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs requis
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche de l'utilisateur dans la table utilisateur
    const query = `
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur
      WHERE email = $1 AND password = $2
    `;

    console.log('🔍 Recherche utilisateur avec email:', email);
    const result = await pool.query(query, [email, password]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé ou mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('✅ Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Réponse de succès pour mobile
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      token: `mobile_token_${user.idtech}_${Date.now()}` // Token simple pour mobile
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// Route pour obtenir les informations d'un utilisateur mobile
app.get('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`� Requête GET /api/user/${id}`);

    const query = `
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur
      WHERE idtech = $1
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log(`✅ Utilisateur ${id} récupéré`);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Utilisateur trouvé'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de l\'utilisateur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'utilisateur',
      error: error.message
    });
  }
});

// Route pour mettre à jour le profil utilisateur mobile
app.put('/api/user/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`� Requête PUT /api/user/${id}:`, req.body);
    const { nom, prenom, adresse, tel, email } = req.body;

    const query = `
      UPDATE utilisateur
      SET nom = $1, prenom = $2, adresse = $3, tel = $4, email = $5
      WHERE idtech = $6
      RETURNING idtech, nom, prenom, adresse, tel, email, role
    `;

    const values = [nom, prenom, adresse, tel, email, id];
    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }

    console.log('✅ Profil utilisateur mis à jour:', result.rows[0]);
    res.json({
      success: true,
      data: result.rows[0],
      message: 'Profil mis à jour avec succès'
    });

  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du profil',
      error: error.message
    });
  }
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`� Serveur d'authentification AquaTrack démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`� Route de connexion: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`👤 Route profil: GET http://localhost:${PORT}/api/user/:id`);
  console.log(`✏️ Route mise à jour: PUT http://localhost:${PORT}/api/user/:id`);
});
