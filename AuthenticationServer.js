const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors());
app.use(express.json());

// Configuration de la base de données "Facturation"
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',  
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ Erreur de connexion à la base de données:', err);
  } else {
    console.log('✅ Connexion à la base de données "Facturation" réussie');
    release();
  }
});

// Route de test
app.get('/', (req, res) => {
  res.json({
    message: 'Serveur d\'authentification AquaTrack',
    status: 'Fonctionnel',
    database: 'Facturation',
    port: PORT,
    endpoints: {
      login: 'POST /api/auth/login',
      testDb: 'GET /api/test-db'
    }
  });
});

// Route de connexion principale
app.post('/api/auth/login', async (req, res) => {
  console.log('🔐 Requête de connexion reçue:', req.body);
  const { email, password } = req.body;

  try {
    // Validation des champs requis
    if (!email || !password) {
      console.log('❌ Champs manquants');
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Recherche de l'utilisateur dans la table utilisateur
    const query = `
      SELECT
        idtech,
        nom,
        prenom,
        adresse,
        tel,
        email,
        role
      FROM utilisateur
      WHERE email = $1 AND password = $2
    `;

    console.log('🔍 Recherche utilisateur avec email:', email);
    const result = await pool.query(query, [email, password]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé ou mot de passe incorrect');
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('✅ Utilisateur trouvé:', user.nom, user.prenom, '- Rôle:', user.role);

    // Vérification du rôle
    if (user.role !== 'Tech' && user.role !== 'Admin') {
      console.log('❌ Rôle non autorisé:', user.role);
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé. Seuls les techniciens et administrateurs peuvent se connecter.'
      });
    }

    // Réponse de succès avec redirection selon le rôle
    const redirectPath = user.role === 'Tech' ? '/technician-dashboard' : '/dashboard-admin';
    
    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role,
        adresse: user.adresse,
        tel: user.tel
      },
      redirectTo: redirectPath,
      token: `auth_token_${user.idtech}_${Date.now()}`
    });

  } catch (error) {
    console.error('❌ Erreur lors de la connexion:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur interne du serveur',
      error: error.message
    });
  }
});

// Route pour tester la base de données et afficher les utilisateurs
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('🔍 Test de la base de données...');
    
    // Test de connexion et récupération des utilisateurs
    const testQuery = `
      SELECT
        idtech,
        nom,
        prenom,
        email,
        role
      FROM utilisateur
      ORDER BY role, nom
    `;
    const result = await pool.query(testQuery);
    
    console.log('✅ Test de base de données réussi');
    console.log('👥 Utilisateurs trouvés:', result.rows.length);
    
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      database: 'Facturation',
      userCount: result.rows.length,
      users: result.rows.map(user => ({
        id: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }))
    });

  } catch (error) {
    console.error('❌ Erreur de test de base de données:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Route pour créer des utilisateurs de test (optionnel)
app.post('/api/create-test-users', async (req, res) => {
  try {
    console.log('👥 Création des utilisateurs de test...');

    // Vérifier si les utilisateurs existent déjà
    const checkQuery = 'SELECT email FROM utilisateur WHERE email IN ($1, $2)';
    const existingUsers = await pool.query(checkQuery, ['<EMAIL>', '<EMAIL>']);

    if (existingUsers.rows.length > 0) {
      return res.json({
        success: true,
        message: 'Les utilisateurs de test existent déjà',
        existing: existingUsers.rows
      });
    }

    // Créer les utilisateurs de test
    const insertQuery = `
      INSERT INTO utilisateur (nom, prenom, adresse, tel, email, password, role, is_protected)
      VALUES 
        ($1, $2, $3, $4, $5, $6, $7, $8),
        ($9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING idtech, nom, prenom, email, role
    `;

    const values = [
      // Technicien
      'Technicien', 'Principal', '456 Rue Technique', '0987654321', 
      '<EMAIL>', 'Tech123', 'Tech', false,
      // Admin
      'Admin', 'Système', '123 Rue Principale', '0123456789', 
      '<EMAIL>', 'Admin123', 'Admin', true
    ];

    const result = await pool.query(insertQuery, values);

    console.log('✅ Utilisateurs de test créés:', result.rows.length);
    res.json({
      success: true,
      message: 'Utilisateurs de test créés avec succès',
      users: result.rows
    });

  } catch (error) {
    console.error('❌ Erreur lors de la création des utilisateurs de test:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création des utilisateurs de test',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err.stack);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Erreur serveur'
  });
});



// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur d'authentification AquaTrack démarré sur le port ${PORT}`);
  console.log(`📱 API disponible sur: http://localhost:${PORT}`);
  console.log(`🔐 Route de connexion: POST http://localhost:${PORT}/api/auth/login`);
  console.log(`🔍 Route test DB: GET http://localhost:${PORT}/api/test-db`);
  console.log(`👥 Route création utilisateurs test: POST http://localhost:${PORT}/api/create-test-users`);
  console.log(`🌐 CORS autorisé pour: localhost:3000, localhost:3001, localhost:3002, localhost:19006`);
  console.log(`📊 Base de données: Facturation`);
  console.log(`📋 Table utilisée: utilisateur`);
  console.log(`🎯 Redirections:`);
  console.log(`   - Tech → /technician-dashboard`);
  console.log(`   - Admin → /dashboard-admin`);
});

module.exports = app;
