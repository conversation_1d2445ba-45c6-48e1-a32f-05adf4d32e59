import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';

// Import des écrans
import TechnicianDashboard from './screens/TechnicianDashboard';
import ClientsListScreen from './screens/ClientsListScreen';
import ConsommationScreen from './screens/ConsommationScreen';
import FacturesScreen from './screens/FacturesScreen';
import ScannerScreen from './screens/ScannerScreen';
import MapScreen from './screens/MapScreen';
import ProfileScreen from './screens/ProfileScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        initialRouteName="Dashboard"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#2196F3',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Dashboard"
          component={TechnicianDashboard}
          options={{
            title: 'AquaTrack - Tableau de Bord',
            headerLeft: null
          }}
        />
        <Stack.Screen
          name="ClientsList"
          component={ClientsListScreen}
          options={{ title: 'Liste des Clients' }}
        />
        <Stack.Screen
          name="Consommation"
          component={ConsommationScreen}
          options={{ title: 'Saisie Consommation' }}
        />
        <Stack.Screen
          name="Factures"
          component={FacturesScreen}
          options={{ title: 'Factures' }}
        />
        <Stack.Screen
          name="Scanner"
          component={ScannerScreen}
          options={{ title: 'Scanner QR Code' }}
        />
        <Stack.Screen
          name="Map"
          component={MapScreen}
          options={{ title: 'Localisation' }}
        />
        <Stack.Screen
          name="Profile"
          component={ProfileScreen}
          options={{ title: 'Profil' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});
